/**
 * @fileoverview Cloud Migration Cost Calculator form submission handler
 * This AWS Lambda function handles cloud migration cost calculator form submissions by:
 * 1. Parsing cloud migration assessment form data from the event
 * 2. Sending assessment data to HubSpot CRM with all cost calculation metrics
 * 3. Sending detailed cost assessment report email via SendGrid
 * 4. Sending notification to Slack with assessment summary
 *
 * Configuration is loaded from AWS SSM Parameter Store with fallback to environment variables.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import sendDataToHubspot from "../../common/sendDataToHubSpot.mjs";
import sendDataToSendGrid from "../../common/sendDataToSendGrid.mjs";
import currentTimestamp from "../../common/currentTimestamp.mjs";
import sendToSlack from "../../common/sendDataToSlack.mjs";
import { getConfigValue } from "../../common/ssmConfig.mjs";

/**
 * AWS Lambda handler for cloud migration cost calculator form submissions
 *
 * @async
 * @function handler
 * @param {Object} event - AWS Lambda event object
 * @param {string} event.body - JSON string containing cloud migration form data
 * @param {Object} event.headers - HTTP headers from the request
 * @param {Object} event.requestContext - AWS Lambda request context
 * @returns {Promise<Object>} Lambda response object with statusCode and body
 *
 * @example
 * // Example event.body structure (includes all contact fields plus cloud migration-specific fields):
 * {
 *   "firstName": "John",
 *   "lastName": "Smith",
 *   "emailAddress": "<EMAIL>",
 *   "companyName": "Tech Corp",
 *   "phoneNumber": "+1234567890",
 *   "currentInfrastructure": "On-premises",
 *   "numberOfServers": "50",
 *   "monthlyInfrastructureCost": "10000",
 *   "estimatedMigrationCost": "75000",
 *   "estimatedMonthlySavings": "3000",
 *   "migrationTimeline": "6-12 months",
 *   "primaryGoals": "Cost reduction, Scalability",
 *   "consent": true
 * }
 *
 * @example
 * // Success response:
 * {
 *   "statusCode": 200,
 *   "body": "{\"message\":\"Form submitted successfully.\",\"hubspotResponse\":\"Cloud migration cost calculator form data sent to HubSpot successfully.\"}"
 * }
 */
export const handler = async (event) => {
  try {
    const form_data = JSON.parse(event.body);

    console.log(currentTimestamp());
    console.log("Cloud Migration Cost Calculator Form Data Received:", form_data);

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'emailAddress', 'companyName'];
    const missingFields = requiredFields.filter(field => !form_data[field]);
    
    if (missingFields.length > 0) {
      console.error("Missing required fields:", missingFields);
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
        },
        body: JSON.stringify({
          message: "Missing required fields",
          missingFields: missingFields
        }),
      };
    }

    // Prepare form data for HubSpot (using cloud migration cost calculator form GUID)
    const hubspotFormGuid = await getConfigValue('HUBSPOT_CLOUD_MIGRATION_FORM_GUID') || 
                           await getConfigValue('HUBSPOT_GET_IN_TOUCH_FORM_GUID'); // Fallback to contact form

    // Send data to HubSpot
    const hubspotResponse = await sendDataToHubspot(form_data, hubspotFormGuid);

    if (hubspotResponse.success) {
      console.log("HubSpot submission successful");

      // Send confirmation email via SendGrid
      const templateId = await getConfigValue('SENDGRID_CLOUD_MIGRATION_FORM_TEMPLATE_ID') ||
                        await getConfigValue('SENDGRID_CONTACT_US_FORM_TEMPLATE_ID'); // Fallback to contact template
      
      const emailRes = await sendDataToSendGrid(form_data, templateId);

      // Send Data to success Slack channel (webhook URL will be determined by sendToSlack)
      await sendToSlack(form_data);

      console.log(currentTimestamp());
      console.log("Cloud Migration Lead Data", form_data);
      console.log("HubSpot Response", hubspotResponse);
      console.log("SendGrid Email Response", emailRes);
      console.log("------------------------------------");

      return {
        statusCode: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
        },
        body: JSON.stringify({
          message: "Form submitted successfully.",
          hubspotResponse: hubspotResponse.message,
        }),
      };
    } else {
      console.error("HubSpot submission failed:", hubspotResponse);
      
      // Send failure notification to Slack
      await sendToSlack({
        ...form_data,
        error: "HubSpot submission failed",
        hubspotError: hubspotResponse.message
      }, true); // true indicates this is a failure notification

      return {
        statusCode: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
          "Access-Control-Allow-Methods": "POST,OPTIONS"
        },
        body: JSON.stringify({
          message: "Failed to submit form to HubSpot",
          error: hubspotResponse.message,
        }),
      };
    }
  } catch (error) {
    console.error("Error in cloud migration cost calculator handler:", error);

    // Send failure notification to Slack
    try {
      await sendToSlack({
        error: "Cloud migration cost calculator handler error",
        errorMessage: error.message || error,
        timestamp: currentTimestamp()
      }, true); // true indicates this is a failure notification
    } catch (slackError) {
      console.error("Failed to send error notification to Slack:", slackError);
    }

    return {
      statusCode: 500,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
        "Access-Control-Allow-Methods": "POST,OPTIONS"
      },
      body: JSON.stringify({
        message: "Internal server error",
        error: error.message || error,
      }),
    };
  }
};
